<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Vthon Academy</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .profile-container {
            max-width: 1200px;
            margin: 0 auto;
            margin-top: 100px;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: calc(100vh - 100px);
        }

        .profile-header {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 40px;
            align-items: center;
            text-align: left;
        }

        .profile-left {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .profile-right {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .profile-avatar {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .profile-avatar img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .profile-name {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin: 0 0 10px 0;
        }

        .profile-class {
            color: #718096;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .profile-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f7fafc;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .card-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        .badges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            max-height: 400px;
            overflow-y: auto;
            padding-right: 10px;
        }

        .badges-grid::-webkit-scrollbar {
            width: 8px;
        }

        .badges-grid::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .badges-grid::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .badges-grid::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .badge-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .badge-item.earned {
            border-color: #48bb78;
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.2);
        }

        .badge-item.earned::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-top: 20px solid #48bb78;
        }

        .badge-item.earned::after {
            content: '✓';
            position: absolute;
            top: 2px;
            right: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .badge-item.not-earned {
            opacity: 0.5;
            background: #f7fafc;
            filter: grayscale(0.7);
        }

        .badge-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #e2e8f0;
            flex-shrink: 0;
        }

        .badge-item.earned .badge-image {
            border-color: #48bb78;
        }

        .badge-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .badge-info {
            flex: 1;
        }

        .badge-name {
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 5px 0;
        }

        .badge-description {
            font-size: 0.9rem;
            color: #718096;
            line-height: 1.4;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #718096;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #e53e3e;
        }

        @media (max-width: 768px) {
            .profile-container {
                padding: 10px;
                margin-top: 80px;
            }

            .profile-header {
                padding: 20px;
                grid-template-columns: 1fr;
                gap: 20px;
                text-align: center;
            }

            .profile-left,
            .profile-right {
                text-align: center;
            }

            .profile-name {
                font-size: 1.5rem;
            }

            .profile-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .badges-grid {
                grid-template-columns: 1fr;
                max-height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html"><i class="fas fa-user"></i> Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Profile Container -->
    <div class="profile-container">
        <a href="../rankings/" class="back-button">
            <i class="fas fa-arrow-left"></i>
            Quay lại Bảng Xếp Hạng
        </a>

        <div id="loadingState" class="loading">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 20px;"></i>
            <p>Đang tải thông tin học viên...</p>
        </div>

        <div id="errorState" class="error" style="display: none;">
            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 20px;"></i>
            <p>Không thể tải thông tin học viên. Vui lòng thử lại sau.</p>
        </div>

        <div id="profileContent" style="display: none;">
            <!-- Profile Header -->
            <div class="profile-header">
                <div class="profile-left">
                    <div class="profile-avatar">
                        <img id="profileAvatarImg" src="../assets/images/user.png" alt="Avatar">
                    </div>
                </div>

                <div class="profile-right">
                    <h1 id="profileName" class="profile-name">Đang tải...</h1>
                    <p id="profileClass" class="profile-class">Đang tải...</p>

                    <div class="profile-stats">
                        <div class="stat-item">
                            <div id="profileAssignments" class="stat-number">0</div>
                            <div class="stat-label">Bài tập</div>
                        </div>
                        <div class="stat-item">
                            <div id="profileScore" class="stat-number">0</div>
                            <div class="stat-label">Điểm tổng</div>
                        </div>
                        <div class="stat-item">
                            <div id="profileBadges" class="stat-number">0</div>
                            <div class="stat-label">Huy hiệu</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Badges Section -->
            <div class="profile-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="card-title">Huy Hiệu & Thành Tích</h3>
                </div>
                
                <div id="badgesGrid" class="badges-grid">
                    <!-- Badges will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Common JavaScript -->
    <script src="../assets/js/script.js"></script>

    <!-- Firebase and JavaScript -->
    <script type="module">
        // Firebase imports
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getAuth, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';
        import { getFirestore, doc, getDoc } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        let availableBadges = [];

        // Get userId from URL parameters
        function getUserIdFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('userId');
        }

        // Load available badges
        async function loadBadges() {
            try {
                const response = await fetch('../assets/images/badges/badges.json');
                const badgesData = await response.json();
                availableBadges = badgesData.badges;
            } catch (error) {
                console.error("Error loading badges:", error);
                availableBadges = [];
            }
        }

        // Load student profile
        async function loadStudentProfile(userId) {
            try {
                const userDoc = await getDoc(doc(db, "users", userId));

                if (!userDoc.exists()) {
                    showError();
                    return;
                }

                const userData = userDoc.data();

                // Update profile information
                document.getElementById('profileAvatarImg').src = userData.avatar || '../assets/images/user.png';
                document.getElementById('profileName').textContent = userData.fullName || 'Chưa cập nhật';
                document.getElementById('profileClass').textContent = getClassDisplayName(userData.courseClass);

                // Update stats
                document.getElementById('profileAssignments').textContent = userData.assignmentCount || 0;
                document.getElementById('profileScore').textContent = userData.totalScore || 0;
                document.getElementById('profileBadges').textContent = (userData.badges || []).length;

                // Load badges
                await loadStudentBadges(userData.badges || []);

                // Show profile content
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('profileContent').style.display = 'block';

            } catch (error) {
                console.error("Error loading student profile:", error);
                showError();
            }
        }

        // Load student badges
        async function loadStudentBadges(studentBadges) {
            const badgesGrid = document.getElementById('badgesGrid');

            if (availableBadges.length === 0) {
                badgesGrid.innerHTML = '<p style="text-align: center; color: #718096;">Không thể tải danh sách huy hiệu.</p>';
                return;
            }

            badgesGrid.innerHTML = availableBadges.map(badge => {
                const isEarned = studentBadges.includes(badge.id);
                return `
                    <div class="badge-item ${isEarned ? 'earned' : 'not-earned'}" title="${badge.name}: ${badge.description}">
                        <div class="badge-image">
                            <img src="../assets/images/badges/${badge.image}" alt="${badge.name}"
                                 onerror="this.src='../assets/images/logo.jpg'">
                        </div>
                        <div class="badge-info">
                            <div class="badge-name">${badge.name}</div>
                            <div class="badge-description">${badge.description}</div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Get class display name
        function getClassDisplayName(classId) {
            const classNames = {
                'python-a': 'Python - A',
                'python-b': 'Python - B',
                'python-c': 'Python - C'
            };
            return classNames[classId] || classId || 'Chưa phân lớp';
        }

        // Show error state
        function showError() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            const userId = getUserIdFromURL();

            if (!userId) {
                showError();
                return;
            }

            // Load badges first
            await loadBadges();

            // Load student profile
            await loadStudentProfile(userId);
        });
    </script>
